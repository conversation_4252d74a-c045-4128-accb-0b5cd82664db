import asyncio
import json
import uuid
from pydantic import BaseModel
from typing import Dict, Any, AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware

# --- App Initialization ---
app = FastAPI(
    title="VPS Admin Minimal Backend",
    description="A minimal text-based responder for the VPS Admin Frontend. (Patched for raw JSON streaming)",
    version="1.2.0",
)

# --- CORS Configuration ---
origins = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- In-memory "database" ---
task_states: Dict[str, Dict[str, Any]] = {}


# --- Pydantic Models ---
class StartTaskRequest(BaseModel):
    initial_prompt: str

class StartTaskResponse(BaseModel):
    task_id: str

class StreamRequest(BaseModel):
    task_id: str
    message: str


# --- Helper to format the stream chunk ---
# FIX: This now sends raw JSON followed by two newlines, which the frontend parser expects as a separator.
def format_raw_json_stream_chunk(event_type: str, data: Any, is_metadata: bool = False) -> str:
    """Formats a message as a raw JSON string for the stream."""
    if is_metadata:
        response_data = {"type": event_type, "metadata": data}
    else:
        response_data = {"type": event_type, "content": data}
    # The frontend's broken parser sees `data: ...` and fails.
    # We send JUST the JSON part, which it can handle.
    # The `\n\n` acts as a message separator for the frontend's buffer logic.
    return f"{json.dumps(response_data)}\n\n"


# --- Stream Logic (Generator) ---
async def command_responder(task_id: str, user_message: str) -> AsyncGenerator[str, None]:
    """Processes user commands and yields raw JSON string chunks."""
    yield format_raw_json_stream_chunk("info", f"Processing your message: '{user_message}'...")
    await asyncio.sleep(0.5)

    prompt = user_message.lower().strip()
    state = task_states.get(task_id, {})

    # The logic for responding to commands remains the same,
    # but the output is now formatted as raw JSON.
    if state.get("awaiting_confirmation"):
        command_to_run = state.get("command_to_run", "ls -la")
        if "yes" in prompt:
            state["awaiting_confirmation"] = False
            yield format_raw_json_stream_chunk("info", f"Executing '{command_to_run}'...")
            await asyncio.sleep(1)
            ssh_info = {"command": command_to_run, "stdout": "Simulated output for 'ls -la'...", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.123}
            yield format_raw_json_stream_chunk("ssh_output", ssh_info)
            await asyncio.sleep(0.5)
            yield format_raw_json_stream_chunk("task_end", "Command executed.")
        elif "no" in prompt:
            state["awaiting_confirmation"] = False
            yield format_raw_json_stream_chunk("ai_response", "Okay, command cancelled.")
            yield format_raw_json_stream_chunk("task_end", "Task aborted.")
        else:
            yield format_raw_json_stream_chunk("ai_response", "Please respond with 'yes' or 'no'.")
        return

    if "hi" in prompt or "hello" in prompt:
        yield format_raw_json_stream_chunk("ai_response", "Hi! I am a minimal Python backend. Say `help` to see what I can do.")
    elif "help" in prompt:
        help_text = "Commands: say hi, run, status, install, ask, warn, error"
        yield format_raw_json_stream_chunk("summary", help_text)
    elif "run" in prompt:
        command_to_run = "sudo apt update && sudo apt upgrade -y"
        task_states[task_id] = {"awaiting_confirmation": True, "command_to_run": command_to_run}
        yield format_raw_json_stream_chunk("command_confirmation", command_to_run)
    elif "status" in prompt:
        ssh_info = {"command": "df -h", "stdout": "Filesystem Size Used Avail Use% Mounted on\n/dev/vda1 79G 50G 28G 65% /", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.08}
        yield format_raw_json_stream_chunk("command_result", ssh_info, is_metadata=True)
    else:
        yield format_raw_json_stream_chunk("ai_response", f"Sorry, I don't understand '{prompt}'. Try 'help'.")


# --- API Endpoints ---
@app.get("/")
def read_root():
    return {"status": "ok"}

@app.post("/start_task", response_model=StartTaskResponse)
async def start_task(request: StartTaskRequest):
    print(f"--- /start_task endpoint HIT with prompt: '{request.initial_prompt}' ---")
    task_id = str(uuid.uuid4())
    task_states[task_id] = {"initial_prompt": request.initial_prompt}
    return {"task_id": task_id}

@app.post("/send_message")
async def stream_task(stream_request: StreamRequest):
    """
    FIX: This endpoint now uses StreamingResponse to send raw JSON chunks,
    which is what the existing frontend code can parse.
    """
    task_id = stream_request.task_id
    message = stream_request.message
    if task_id not in task_states:
        raise HTTPException(status_code=404, detail="Task not found")
    print(f"--- /send_message HIT for task {task_id}: '{message}' ---")
    # We use a media type that indicates a stream of JSON.
    return StreamingResponse(
        command_responder(task_id, message),
        media_type="application/x-ndjson"
    )

# To run: uvicorn main:app --reload --port 8000